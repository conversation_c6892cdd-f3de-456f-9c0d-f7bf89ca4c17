2025-06-12 12:48:21 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 12:48:22 - agent.assistant_agent - INFO - 初始化助手代理: 测试助手, 工具数量: 4
2025-06-12 12:49:24 - agent - INFO - ==================================================
2025-06-12 12:49:24 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 12:49:24 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 12:49:24 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 12:49:24 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 12:49:24 - agent - INFO - 依赖包版本:
2025-06-12 12:49:24 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 12:49:24 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 12:49:24 - agent - INFO -   openai: 1.86.0
2025-06-12 12:49:24 - agent - INFO - ==================================================
2025-06-12 12:49:55 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 12:49:56 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 12:49:56 - agent.api - INFO - 创建新代理: afd13619-de68-4cff-a0a6-4f62d9152705
2025-06-12 12:50:13 - agent.api - INFO - 代理 afd13619-de68-4cff-a0a6-4f62d9152705 响应: 你好！我是一个AI助手，我的职责是帮助你完成各种任务，无论是查找信息、进行计算还是提供天气预报等。如...
2025-06-12 12:55:13 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 12:55:13 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 12:55:13 - agent.api - INFO - 创建新代理: c52bba49-1d05-4feb-a653-341c5ca30a69
2025-06-12 12:55:22 - agent.api - INFO - 代理 c52bba49-1d05-4feb-a653-341c5ca30a69 响应: 你好！很高兴见到你。...
2025-06-12 12:55:27 - agent.api - INFO - 开始为代理 c52bba49-1d05-4feb-a653-341c5ca30a69 生成流式响应
2025-06-12 12:55:46 - agent.api - INFO - 代理 c52bba49-1d05-4feb-a653-341c5ca30a69 完成流式响应，共 22 个块
2025-06-12 12:57:20 - agent.api - INFO - 开始为代理 c52bba49-1d05-4feb-a653-341c5ca30a69 生成流式响应
2025-06-12 12:57:39 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 12:57:39 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 12:57:39 - agent.api - INFO - 创建新代理: 204ff515-cc5f-4e41-b133-313d9ee22f6c
2025-06-12 12:57:47 - agent.api - INFO - 代理 204ff515-cc5f-4e41-b133-313d9ee22f6c 响应: 你好！很高兴见到你。...
2025-06-12 12:57:51 - agent.api - INFO - 开始为代理 204ff515-cc5f-4e41-b133-313d9ee22f6c 生成流式响应
2025-06-12 12:58:07 - agent.api - INFO - 代理 204ff515-cc5f-4e41-b133-313d9ee22f6c 完成流式响应，共 17 个块
2025-06-12 12:58:07 - agent.api - INFO - 开始为代理 204ff515-cc5f-4e41-b133-313d9ee22f6c 生成流式响应
2025-06-12 13:34:57 - agent - INFO - ==================================================
2025-06-12 13:34:57 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:34:57 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:34:57 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:34:57 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:34:57 - agent - INFO - 依赖包版本:
2025-06-12 13:34:57 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:34:57 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:34:57 - agent - INFO -   openai: 1.86.0
2025-06-12 13:34:57 - agent - INFO - ==================================================
2025-06-12 13:36:35 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:36:36 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:36:36 - agent.api - INFO - 创建新代理: c8333bb2-198e-4473-86c3-c582f70941d8
2025-06-12 13:36:46 - agent.api - INFO - 代理 c8333bb2-198e-4473-86c3-c582f70941d8 响应: 你好！很高兴见到你。...
2025-06-12 13:36:51 - agent.api - INFO - 开始为代理 c8333bb2-198e-4473-86c3-c582f70941d8 生成流式响应
2025-06-12 13:37:08 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 13:37:09 - agent - INFO - ==================================================
2025-06-12 13:37:09 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:37:09 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:37:09 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:37:09 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:37:09 - agent - INFO - 依赖包版本:
2025-06-12 13:37:09 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:37:09 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:37:09 - agent - INFO -   openai: 1.86.0
2025-06-12 13:37:09 - agent - INFO - ==================================================
2025-06-12 13:37:29 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:37:30 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:37:30 - agent.api - INFO - 创建新代理: 0bf88991-8033-4d01-a756-c608c6a5b16d
2025-06-12 13:37:30 - agent.api - INFO - 开始为代理 0bf88991-8033-4d01-a756-c608c6a5b16d 生成流式响应
2025-06-12 13:37:41 - agent.api - INFO - 代理 0bf88991-8033-4d01-a756-c608c6a5b16d 完成流式响应，共 7 个块
2025-06-12 13:38:10 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 13:38:12 - agent - INFO - ==================================================
2025-06-12 13:38:12 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:38:12 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:38:12 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:38:12 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:38:12 - agent - INFO - 依赖包版本:
2025-06-12 13:38:12 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:38:12 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:38:12 - agent - INFO -   openai: 1.86.0
2025-06-12 13:38:12 - agent - INFO - ==================================================
2025-06-12 13:38:36 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 13:38:38 - agent - INFO - ==================================================
2025-06-12 13:38:38 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:38:38 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:38:38 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:38:38 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:38:38 - agent - INFO - 依赖包版本:
2025-06-12 13:38:38 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:38:38 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:38:38 - agent - INFO -   openai: 1.86.0
2025-06-12 13:38:38 - agent - INFO - ==================================================
2025-06-12 13:38:40 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 13:38:42 - agent - INFO - ==================================================
2025-06-12 13:38:42 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:38:42 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:38:42 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:38:42 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:38:42 - agent - INFO - 依赖包版本:
2025-06-12 13:38:42 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:38:42 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:38:42 - agent - INFO -   openai: 1.86.0
2025-06-12 13:38:42 - agent - INFO - ==================================================
2025-06-12 13:38:52 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:38:53 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:38:53 - agent.api - INFO - 创建新代理: d8c6f600-f4e0-4e0a-8580-fa28216a5cc8
2025-06-12 13:39:01 - agent.api - INFO - 代理 d8c6f600-f4e0-4e0a-8580-fa28216a5cc8 响应: 你好！很高兴见到你。...
2025-06-12 13:39:01 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:39:01 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:39:01 - agent.api - INFO - 创建新代理: 46ab5ec4-fed7-4b88-89ce-270de3a4c6f3
2025-06-12 13:39:13 - agent.api - INFO - 代理 46ab5ec4-fed7-4b88-89ce-270de3a4c6f3 响应: 你好！我是一个AI助手，旨在帮助你解决各种问题，无论是查找信息、执行计算还是提供日常帮助。如果有任何...
2025-06-12 13:39:13 - agent.api - INFO - 开始为代理 46ab5ec4-fed7-4b88-89ce-270de3a4c6f3 生成流式响应
2025-06-12 13:39:28 - agent.api - INFO - 开始为代理 d8c6f600-f4e0-4e0a-8580-fa28216a5cc8 生成流式响应
2025-06-12 13:39:47 - agent.api - INFO - 代理 d8c6f600-f4e0-4e0a-8580-fa28216a5cc8 完成流式响应，共 30 个块
2025-06-12 13:39:47 - agent.api - INFO - 代理 46ab5ec4-fed7-4b88-89ce-270de3a4c6f3 完成流式响应，共 101 个块
2025-06-12 13:39:47 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:39:47 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:39:47 - agent.api - INFO - 创建新代理: 445dc65f-a77b-4a5c-b88e-8fa98160ffb7
2025-06-12 13:39:47 - agent.api - INFO - 开始为代理 445dc65f-a77b-4a5c-b88e-8fa98160ffb7 生成流式响应
2025-06-12 13:40:03 - agent.api - INFO - 代理 445dc65f-a77b-4a5c-b88e-8fa98160ffb7 完成流式响应，共 8 个块
2025-06-12 13:41:45 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 13:41:47 - agent - INFO - ==================================================
2025-06-12 13:41:47 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:41:47 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:41:47 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:41:47 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:41:47 - agent - INFO - 依赖包版本:
2025-06-12 13:41:47 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:41:47 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:41:47 - agent - INFO -   openai: 1.86.0
2025-06-12 13:41:47 - agent - INFO - ==================================================
2025-06-12 13:43:05 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:43:06 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:43:06 - agent.api - INFO - 创建新代理: 79ad60c3-9f8b-4bcb-ba81-89d7f434d2ad
2025-06-12 13:43:08 - agent.api - INFO - 开始为代理 79ad60c3-9f8b-4bcb-ba81-89d7f434d2ad 生成流式响应
2025-06-12 13:43:27 - agent.api - INFO - 代理 79ad60c3-9f8b-4bcb-ba81-89d7f434d2ad 完成流式响应，共 16 个块
2025-06-12 13:43:36 - agent.api - INFO - 开始为代理 79ad60c3-9f8b-4bcb-ba81-89d7f434d2ad 生成流式响应
2025-06-12 13:43:52 - agent.api - INFO - 代理 79ad60c3-9f8b-4bcb-ba81-89d7f434d2ad 完成流式响应，共 12 个块
2025-06-12 13:45:15 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:45:15 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:45:15 - agent.api - INFO - 创建新代理: f525a83f-1bf1-4beb-a989-0763947667cf
2025-06-12 13:45:24 - agent.api - INFO - 开始为代理 f525a83f-1bf1-4beb-a989-0763947667cf 生成流式响应
2025-06-12 13:45:40 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:45:40 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:45:40 - agent.api - INFO - 创建新代理: 44adfc1d-12e5-45ca-984c-c69347fcf545
2025-06-12 13:45:43 - agent.api - INFO - 代理 f525a83f-1bf1-4beb-a989-0763947667cf 完成流式响应，共 19 个块
2025-06-12 13:45:48 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:45:48 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:45:48 - agent.api - INFO - 创建新代理: 7900f6f2-d431-42c6-8dd6-c505c5e5345f
2025-06-12 13:45:50 - agent.api - INFO - 开始为代理 7900f6f2-d431-42c6-8dd6-c505c5e5345f 生成流式响应
2025-06-12 13:46:17 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:46:17 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:46:17 - agent.api - INFO - 创建新代理: 15976953-2efd-410d-b93d-e4e16c4836b3
2025-06-12 13:46:18 - agent.api - INFO - 开始为代理 f525a83f-1bf1-4beb-a989-0763947667cf 生成流式响应
2025-06-12 13:46:30 - agent.api - INFO - 开始为代理 f525a83f-1bf1-4beb-a989-0763947667cf 生成流式响应
2025-06-12 13:46:48 - agent.api - INFO - 代理 f525a83f-1bf1-4beb-a989-0763947667cf 完成流式响应，共 18 个块
2025-06-12 13:46:48 - agent.api - INFO - 代理 7900f6f2-d431-42c6-8dd6-c505c5e5345f 完成流式响应，共 140 个块
2025-06-12 13:46:50 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:46:50 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:46:50 - agent.api - INFO - 创建新代理: 5a9bcfaf-4ed4-477f-983b-440a5fb23df6
2025-06-12 13:46:58 - agent.api - INFO - 开始为代理 5a9bcfaf-4ed4-477f-983b-440a5fb23df6 生成流式响应
2025-06-12 13:47:17 - agent.api - INFO - 代理 5a9bcfaf-4ed4-477f-983b-440a5fb23df6 完成流式响应，共 27 个块
2025-06-12 13:48:06 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:06 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:06 - agent.api - INFO - 创建新代理: a2da1cde-5904-4970-9eb1-d7117f2106fe
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:07 - agent.api - INFO - 创建新代理: 071e82c7-858d-4ed5-bb08-b5ac5b1205cb
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:07 - agent.api - INFO - 创建新代理: 4513e3d5-d8ef-4b53-a495-1c940ac1ddcf
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:07 - agent.api - INFO - 创建新代理: d5f20278-c302-438e-b025-bf3705fb42fd
2025-06-12 13:48:07 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:08 - agent.api - INFO - 创建新代理: d03895d3-0ac5-45c1-b605-8d14c48cb252
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:08 - agent.api - INFO - 创建新代理: 649d8367-dbe3-447a-a690-916a32b68276
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:08 - agent.api - INFO - 创建新代理: 576517fe-8f03-4d6f-a3fe-b04ec85b9271
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:08 - agent.api - INFO - 创建新代理: dd4687ef-aa43-4bb9-918a-985b3e696adc
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:08 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:08 - agent.api - INFO - 创建新代理: 96d4abe4-e0ec-4571-9c44-64c1a23d755e
2025-06-12 13:48:23 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 13:48:25 - agent - INFO - ==================================================
2025-06-12 13:48:25 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 13:48:25 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 13:48:25 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 13:48:25 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 13:48:25 - agent - INFO - 依赖包版本:
2025-06-12 13:48:25 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 13:48:25 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 13:48:25 - agent - INFO -   openai: 1.86.0
2025-06-12 13:48:25 - agent - INFO - ==================================================
2025-06-12 13:48:30 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 13:48:31 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 13:48:31 - agent.api - INFO - 创建新代理: 85aaa4f7-b2de-4c79-9ba8-adf572b08a7d
2025-06-12 14:07:06 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 14:07:06 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 14:07:06 - agent.api - INFO - 创建新代理: 0b658f87-ee20-49ba-a338-05b28a8252a4
2025-06-12 14:08:15 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 14:08:15 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 14:08:15 - agent.api - INFO - 创建新代理: 463fed28-1a90-4598-a39a-90f529a105f6
2025-06-12 14:08:19 - agent.api - INFO - 开始为代理 463fed28-1a90-4598-a39a-90f529a105f6 生成流式响应
2025-06-12 14:08:59 - agent.api - INFO - 代理 463fed28-1a90-4598-a39a-90f529a105f6 完成流式响应，共 106 个块
2025-06-12 14:11:14 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 14:11:14 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 14:11:14 - agent.api - INFO - 创建新代理: f6363adc-b24c-4e10-b189-0b4c6677248b
2025-06-12 14:11:16 - agent.api - INFO - 开始为代理 f6363adc-b24c-4e10-b189-0b4c6677248b 生成流式响应
2025-06-12 14:11:35 - agent.api - INFO - 代理 f6363adc-b24c-4e10-b189-0b4c6677248b 完成流式响应，共 15 个块
2025-06-12 14:14:51 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 14:14:51 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 14:14:51 - agent.api - INFO - 创建新代理: 04d55568-b3d6-4b10-9aa1-e84a5d3dec60
2025-06-12 14:14:53 - agent.api - INFO - 开始为代理 04d55568-b3d6-4b10-9aa1-e84a5d3dec60 生成流式响应
2025-06-12 14:15:11 - agent.api - INFO - 代理 04d55568-b3d6-4b10-9aa1-e84a5d3dec60 完成流式响应，共 19 个块
2025-06-12 14:16:27 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 14:16:27 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 14:16:27 - agent.api - INFO - 创建新代理: 0429b737-3410-4b13-8742-80d3dbafdc5d
2025-06-12 14:16:28 - agent.api - INFO - 开始为代理 0429b737-3410-4b13-8742-80d3dbafdc5d 生成流式响应
2025-06-12 14:17:04 - agent.api - INFO - 代理 0429b737-3410-4b13-8742-80d3dbafdc5d 完成流式响应，共 64 个块
2025-06-12 14:17:28 - agent.api - INFO - 开始为代理 0429b737-3410-4b13-8742-80d3dbafdc5d 生成流式响应
2025-06-12 14:17:59 - agent.api - INFO - 代理 0429b737-3410-4b13-8742-80d3dbafdc5d 完成流式响应，共 55 个块
2025-06-12 14:25:27 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:25:30 - agent - INFO - ==================================================
2025-06-12 14:25:30 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:25:30 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:25:30 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:25:30 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:25:30 - agent - INFO - 依赖包版本:
2025-06-12 14:25:30 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:25:30 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:25:30 - agent - INFO -   openai: 1.86.0
2025-06-12 14:25:30 - agent - INFO - ==================================================
2025-06-12 14:25:57 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:25:59 - agent - INFO - ==================================================
2025-06-12 14:25:59 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:25:59 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:25:59 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:25:59 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:25:59 - agent - INFO - 依赖包版本:
2025-06-12 14:25:59 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:25:59 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:25:59 - agent - INFO -   openai: 1.86.0
2025-06-12 14:25:59 - agent - INFO - ==================================================
2025-06-12 14:26:29 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:26:31 - agent - INFO - ==================================================
2025-06-12 14:26:31 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:26:31 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:26:31 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:26:31 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:26:31 - agent - INFO - 依赖包版本:
2025-06-12 14:26:31 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:26:31 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:26:31 - agent - INFO -   openai: 1.86.0
2025-06-12 14:26:31 - agent - INFO - ==================================================
2025-06-12 14:26:41 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:26:43 - agent - INFO - ==================================================
2025-06-12 14:26:43 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:26:43 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:26:43 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:26:43 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:26:43 - agent - INFO - 依赖包版本:
2025-06-12 14:26:43 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:26:43 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:26:43 - agent - INFO -   openai: 1.86.0
2025-06-12 14:26:43 - agent - INFO - ==================================================
2025-06-12 14:26:49 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:26:51 - agent - INFO - ==================================================
2025-06-12 14:26:51 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:26:51 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:26:51 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:26:51 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:26:51 - agent - INFO - 依赖包版本:
2025-06-12 14:26:51 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:26:51 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:26:51 - agent - INFO -   openai: 1.86.0
2025-06-12 14:26:51 - agent - INFO - ==================================================
2025-06-12 14:27:07 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:27:09 - agent - INFO - ==================================================
2025-06-12 14:27:09 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:27:09 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:27:09 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:27:09 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:27:09 - agent - INFO - 依赖包版本:
2025-06-12 14:27:09 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:27:09 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:27:09 - agent - INFO -   openai: 1.86.0
2025-06-12 14:27:09 - agent - INFO - ==================================================
2025-06-12 14:27:17 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:27:19 - agent - INFO - ==================================================
2025-06-12 14:27:19 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:27:19 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:27:19 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:27:19 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:27:19 - agent - INFO - 依赖包版本:
2025-06-12 14:27:19 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:27:19 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:27:19 - agent - INFO -   openai: 1.86.0
2025-06-12 14:27:19 - agent - INFO - ==================================================
2025-06-12 14:27:37 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:27:39 - agent - INFO - ==================================================
2025-06-12 14:27:39 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:27:39 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:27:39 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:27:39 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:27:39 - agent - INFO - 依赖包版本:
2025-06-12 14:27:39 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:27:39 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:27:39 - agent - INFO -   openai: 1.86.0
2025-06-12 14:27:39 - agent - INFO - ==================================================
2025-06-12 14:29:58 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:30:01 - agent - INFO - ==================================================
2025-06-12 14:30:01 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:30:01 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:30:01 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:30:01 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:30:01 - agent - INFO - 依赖包版本:
2025-06-12 14:30:01 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:30:01 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:30:01 - agent - INFO -   openai: 1.86.0
2025-06-12 14:30:01 - agent - INFO - ==================================================
2025-06-12 14:30:40 - agent.testcase_agent - INFO - 初始化测试用例编写代理: 测试用例编写助手
2025-06-12 14:30:40 - agent.api - INFO - 创建测试用例编写代理: 26be307d-0bbf-4e79-809f-138c9bf0c384
2025-06-12 14:30:43 - agent.api - INFO - 开始为代理 26be307d-0bbf-4e79-809f-138c9bf0c384 生成流式响应
2025-06-12 14:32:20 - agent.testcase_agent - INFO - 初始化测试用例编写代理: 测试用例编写助手
2025-06-12 14:32:20 - agent.api - INFO - 创建测试用例编写代理: 8cef08ba-cc86-4608-ab6e-9136dc9cf918
2025-06-12 14:35:10 - agent - INFO - ==================================================
2025-06-12 14:35:10 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:35:10 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:35:10 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:35:10 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:35:10 - agent - INFO - 依赖包版本:
2025-06-12 14:35:10 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:35:10 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:35:10 - agent - INFO -   openai: 1.86.0
2025-06-12 14:35:10 - agent - INFO - ==================================================
2025-06-12 14:35:18 - agent.testcase_agent - INFO - 初始化测试用例编写代理: 测试用例编写助手
2025-06-12 14:35:18 - agent.api - INFO - 创建测试用例编写代理: 48325004-01a7-4954-a78f-679c2e3b1056
2025-06-12 14:35:40 - agent.testcase_agent - INFO - 初始化测试用例编写代理: 测试用例编写助手
2025-06-12 14:35:40 - agent.api - INFO - 创建测试用例编写代理: 19b8b57e-5131-4f77-b263-ab16150eea1d
2025-06-12 14:37:30 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:37:32 - agent - INFO - ==================================================
2025-06-12 14:37:32 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:37:32 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:37:32 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:37:32 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:37:32 - agent - INFO - 依赖包版本:
2025-06-12 14:37:32 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:37:32 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:37:32 - agent - INFO -   openai: 1.86.0
2025-06-12 14:37:32 - agent - INFO - ==================================================
2025-06-12 14:39:21 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 14:39:22 - agent.assistant_agent - INFO - 初始化助手代理: AI助手, 工具数量: 4
2025-06-12 14:39:22 - agent.api - INFO - 创建新的通用助手代理: 9e3a9940-0149-490f-bff0-d43df3a2ead3
2025-06-12 14:39:22 - agent.api - INFO - 开始为代理 9e3a9940-0149-490f-bff0-d43df3a2ead3 生成流式响应
2025-06-12 14:40:17 - agent.api - INFO - 代理 9e3a9940-0149-490f-bff0-d43df3a2ead3 完成流式响应，共 75 个块
2025-06-12 14:41:41 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:41:43 - agent - INFO - ==================================================
2025-06-12 14:41:43 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:41:43 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:41:43 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:41:43 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:41:43 - agent - INFO - 依赖包版本:
2025-06-12 14:41:43 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:41:43 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:41:43 - agent - INFO -   openai: 1.86.0
2025-06-12 14:41:43 - agent - INFO - ==================================================
2025-06-12 14:41:54 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:41:56 - agent - INFO - ==================================================
2025-06-12 14:41:56 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:41:56 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:41:56 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:41:56 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:41:56 - agent - INFO - 依赖包版本:
2025-06-12 14:41:56 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:41:56 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:41:56 - agent - INFO -   openai: 1.86.0
2025-06-12 14:41:56 - agent - INFO - ==================================================
2025-06-12 14:42:06 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:42:08 - agent - INFO - ==================================================
2025-06-12 14:42:08 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:42:08 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:42:08 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:42:08 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:42:08 - agent - INFO - 依赖包版本:
2025-06-12 14:42:08 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:42:08 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:42:08 - agent - INFO -   openai: 1.86.0
2025-06-12 14:42:08 - agent - INFO - ==================================================
2025-06-12 14:42:18 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:42:20 - agent - INFO - ==================================================
2025-06-12 14:42:20 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:42:20 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:42:20 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:42:20 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:42:20 - agent - INFO - 依赖包版本:
2025-06-12 14:42:20 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:42:20 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:42:20 - agent - INFO -   openai: 1.86.0
2025-06-12 14:42:20 - agent - INFO - ==================================================
2025-06-12 14:42:29 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:42:31 - agent - INFO - ==================================================
2025-06-12 14:42:31 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:42:31 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:42:31 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:42:31 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:42:31 - agent - INFO - 依赖包版本:
2025-06-12 14:42:31 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:42:31 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:42:31 - agent - INFO -   openai: 1.86.0
2025-06-12 14:42:31 - agent - INFO - ==================================================
2025-06-12 14:42:58 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:43:00 - agent - INFO - ==================================================
2025-06-12 14:43:00 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:43:00 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:43:00 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:43:00 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:43:00 - agent - INFO - 依赖包版本:
2025-06-12 14:43:00 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:43:00 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:43:00 - agent - INFO -   openai: 1.86.0
2025-06-12 14:43:00 - agent - INFO - ==================================================
2025-06-12 14:43:31 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:43:33 - agent - INFO - ==================================================
2025-06-12 14:43:33 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:43:33 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:43:33 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:43:33 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:43:33 - agent - INFO - 依赖包版本:
2025-06-12 14:43:33 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:43:33 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:43:33 - agent - INFO -   openai: 1.86.0
2025-06-12 14:43:33 - agent - INFO - ==================================================
2025-06-12 14:44:20 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:44:22 - agent - INFO - ==================================================
2025-06-12 14:44:22 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:44:22 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:44:22 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:44:22 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:44:22 - agent - INFO - 依赖包版本:
2025-06-12 14:44:22 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:44:22 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:44:22 - agent - INFO -   openai: 1.86.0
2025-06-12 14:44:22 - agent - INFO - ==================================================
2025-06-12 14:44:25 - agent.testcase_agent - INFO - 初始化测试用例编写代理: 测试用例编写助手
2025-06-12 14:44:25 - agent.api - INFO - 创建测试用例编写代理: cc5734b2-2450-496d-8752-1191a517faa3
2025-06-12 14:44:26 - agent.api - INFO - 开始为代理 cc5734b2-2450-496d-8752-1191a517faa3 生成流式响应
2025-06-12 14:45:33 - agent.testcase_agent - INFO - 初始化测试用例编写代理: 测试用例编写助手
2025-06-12 14:45:33 - agent.api - INFO - 创建测试用例编写代理: f8f0ba7e-26ad-4390-8cad-b9ac3609580d
2025-06-12 14:45:41 - agent.api - INFO - 开始为代理 f8f0ba7e-26ad-4390-8cad-b9ac3609580d 生成流式响应
2025-06-12 14:47:00 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:47:02 - agent - INFO - ==================================================
2025-06-12 14:47:02 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:47:02 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:47:02 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:47:02 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:47:02 - agent - INFO - 依赖包版本:
2025-06-12 14:47:02 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:47:02 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:47:02 - agent - INFO -   openai: 1.86.0
2025-06-12 14:47:02 - agent - INFO - ==================================================
2025-06-12 14:47:38 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:47:40 - agent - INFO - ==================================================
2025-06-12 14:47:40 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:47:40 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:47:40 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:47:40 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:47:40 - agent - INFO - 依赖包版本:
2025-06-12 14:47:40 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:47:40 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:47:40 - agent - INFO -   openai: 1.86.0
2025-06-12 14:47:40 - agent - INFO - ==================================================
2025-06-12 14:48:07 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:48:09 - agent - INFO - ==================================================
2025-06-12 14:48:09 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:48:09 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:48:09 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:48:09 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:48:09 - agent - INFO - 依赖包版本:
2025-06-12 14:48:09 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:48:09 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:48:09 - agent - INFO -   openai: 1.86.0
2025-06-12 14:48:09 - agent - INFO - ==================================================
2025-06-12 14:49:14 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:49:16 - agent - INFO - ==================================================
2025-06-12 14:49:16 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:49:16 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:49:16 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:49:16 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:49:16 - agent - INFO - 依赖包版本:
2025-06-12 14:49:16 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:49:16 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:49:16 - agent - INFO -   openai: 1.86.0
2025-06-12 14:49:16 - agent - INFO - ==================================================
2025-06-12 14:49:43 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:49:45 - agent - INFO - ==================================================
2025-06-12 14:49:45 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:49:45 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:49:45 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:49:45 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:49:45 - agent - INFO - 依赖包版本:
2025-06-12 14:49:45 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:49:45 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:49:45 - agent - INFO -   openai: 1.86.0
2025-06-12 14:49:45 - agent - INFO - ==================================================
2025-06-12 14:50:19 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:50:20 - agent - INFO - ==================================================
2025-06-12 14:50:20 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:50:20 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:50:20 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:50:20 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:50:20 - agent - INFO - 依赖包版本:
2025-06-12 14:50:20 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:50:20 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:50:20 - agent - INFO -   openai: 1.86.0
2025-06-12 14:50:20 - agent - INFO - ==================================================
2025-06-12 14:50:32 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:50:34 - agent - INFO - ==================================================
2025-06-12 14:50:34 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:50:34 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:50:34 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:50:34 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:50:34 - agent - INFO - 依赖包版本:
2025-06-12 14:50:34 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:50:34 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:50:34 - agent - INFO -   openai: 1.86.0
2025-06-12 14:50:34 - agent - INFO - ==================================================
2025-06-12 14:50:50 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:50:52 - agent - INFO - ==================================================
2025-06-12 14:50:52 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:50:52 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:50:52 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:50:52 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:50:52 - agent - INFO - 依赖包版本:
2025-06-12 14:50:52 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:50:52 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:50:52 - agent - INFO -   openai: 1.86.0
2025-06-12 14:50:52 - agent - INFO - ==================================================
2025-06-12 14:51:05 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:51:07 - agent - INFO - ==================================================
2025-06-12 14:51:07 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:51:07 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:51:07 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:51:07 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:51:07 - agent - INFO - 依赖包版本:
2025-06-12 14:51:07 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:51:07 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:51:07 - agent - INFO -   openai: 1.86.0
2025-06-12 14:51:07 - agent - INFO - ==================================================
2025-06-12 14:51:22 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:51:24 - agent - INFO - ==================================================
2025-06-12 14:51:24 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:51:24 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:51:24 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:51:24 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:51:24 - agent - INFO - 依赖包版本:
2025-06-12 14:51:24 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:51:24 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:51:24 - agent - INFO -   openai: 1.86.0
2025-06-12 14:51:24 - agent - INFO - ==================================================
2025-06-12 14:51:57 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 14:52:00 - agent - INFO - ==================================================
2025-06-12 14:52:00 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 14:52:00 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 14:52:00 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 14:52:00 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 14:52:00 - agent - INFO - 依赖包版本:
2025-06-12 14:52:00 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 14:52:00 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 14:52:00 - agent - INFO -   openai: 1.86.0
2025-06-12 14:52:00 - agent - INFO - ==================================================
2025-06-12 15:07:36 - agent - INFO - ==================================================
2025-06-12 15:07:36 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:07:36 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:07:36 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:07:36 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:07:36 - agent - INFO - 依赖包版本:
2025-06-12 15:07:36 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:07:36 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:07:36 - agent - INFO -   openai: 1.86.0
2025-06-12 15:07:36 - agent - INFO - ==================================================
2025-06-12 15:11:05 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:11:07 - agent - INFO - ==================================================
2025-06-12 15:11:07 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:11:07 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:11:07 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:11:07 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:11:07 - agent - INFO - 依赖包版本:
2025-06-12 15:11:07 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:11:07 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:11:07 - agent - INFO -   openai: 1.86.0
2025-06-12 15:11:07 - agent - INFO - ==================================================
2025-06-12 15:11:59 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:12:01 - agent - INFO - ==================================================
2025-06-12 15:12:01 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:12:01 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:12:01 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:12:01 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:12:01 - agent - INFO - 依赖包版本:
2025-06-12 15:12:01 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:12:01 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:12:01 - agent - INFO -   openai: 1.86.0
2025-06-12 15:12:01 - agent - INFO - ==================================================
2025-06-12 15:12:11 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:12:14 - agent - INFO - ==================================================
2025-06-12 15:12:14 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:12:14 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:12:14 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:12:14 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:12:14 - agent - INFO - 依赖包版本:
2025-06-12 15:12:14 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:12:14 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:12:14 - agent - INFO -   openai: 1.86.0
2025-06-12 15:12:14 - agent - INFO - ==================================================
2025-06-12 15:12:32 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:12:36 - agent - INFO - ==================================================
2025-06-12 15:12:36 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:12:36 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:12:36 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:12:36 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:12:36 - agent - INFO - 依赖包版本:
2025-06-12 15:12:36 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:12:36 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:12:36 - agent - INFO -   openai: 1.86.0
2025-06-12 15:12:36 - agent - INFO - ==================================================
2025-06-12 15:12:48 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:12:51 - agent - INFO - ==================================================
2025-06-12 15:12:51 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:12:51 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:12:51 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:12:51 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:12:51 - agent - INFO - 依赖包版本:
2025-06-12 15:12:51 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:12:51 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:12:51 - agent - INFO -   openai: 1.86.0
2025-06-12 15:12:51 - agent - INFO - ==================================================
2025-06-12 15:12:59 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:13:02 - agent - INFO - ==================================================
2025-06-12 15:13:02 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:13:02 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:13:02 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:13:02 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:13:02 - agent - INFO - 依赖包版本:
2025-06-12 15:13:02 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:13:02 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:13:02 - agent - INFO -   openai: 1.86.0
2025-06-12 15:13:02 - agent - INFO - ==================================================
2025-06-12 15:13:17 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:13:20 - agent - INFO - ==================================================
2025-06-12 15:13:20 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:13:20 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:13:20 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:13:20 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:13:20 - agent - INFO - 依赖包版本:
2025-06-12 15:13:20 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:13:20 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:13:20 - agent - INFO -   openai: 1.86.0
2025-06-12 15:13:20 - agent - INFO - ==================================================
2025-06-12 15:13:45 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:13:48 - agent - INFO - ==================================================
2025-06-12 15:13:48 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:13:48 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:13:48 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:13:48 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:13:48 - agent - INFO - 依赖包版本:
2025-06-12 15:13:48 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:13:48 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:13:48 - agent - INFO -   openai: 1.86.0
2025-06-12 15:13:48 - agent - INFO - ==================================================
2025-06-12 15:13:59 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:14:02 - agent - INFO - ==================================================
2025-06-12 15:14:02 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:14:02 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:14:02 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:14:02 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:14:02 - agent - INFO - 依赖包版本:
2025-06-12 15:14:02 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:14:02 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:14:02 - agent - INFO -   openai: 1.86.0
2025-06-12 15:14:02 - agent - INFO - ==================================================
2025-06-12 15:14:15 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:14:18 - agent - INFO - ==================================================
2025-06-12 15:14:18 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:14:18 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:14:18 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:14:18 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:14:18 - agent - INFO - 依赖包版本:
2025-06-12 15:14:18 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:14:18 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:14:18 - agent - INFO -   openai: 1.86.0
2025-06-12 15:14:18 - agent - INFO - ==================================================
2025-06-12 15:14:26 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:14:29 - agent - INFO - ==================================================
2025-06-12 15:14:29 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:14:29 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:14:29 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:14:29 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:14:29 - agent - INFO - 依赖包版本:
2025-06-12 15:14:29 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:14:29 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:14:29 - agent - INFO -   openai: 1.86.0
2025-06-12 15:14:29 - agent - INFO - ==================================================
2025-06-12 15:14:45 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:14:48 - agent - INFO - ==================================================
2025-06-12 15:14:48 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:14:48 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:14:48 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:14:48 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:14:48 - agent - INFO - 依赖包版本:
2025-06-12 15:14:48 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:14:48 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:14:48 - agent - INFO -   openai: 1.86.0
2025-06-12 15:14:48 - agent - INFO - ==================================================
2025-06-12 15:14:55 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:14:58 - agent - INFO - ==================================================
2025-06-12 15:14:58 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:14:58 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:14:58 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:14:58 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:14:58 - agent - INFO - 依赖包版本:
2025-06-12 15:14:58 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:14:58 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:14:58 - agent - INFO -   openai: 1.86.0
2025-06-12 15:14:58 - agent - INFO - ==================================================
2025-06-12 15:15:12 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:19:15 - agent - INFO - ==================================================
2025-06-12 15:19:15 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:19:15 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:19:15 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:19:15 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:19:15 - agent - INFO - 依赖包版本:
2025-06-12 15:19:15 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:19:15 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:19:15 - agent - INFO -   openai: 1.86.0
2025-06-12 15:19:15 - agent - INFO - ==================================================
2025-06-12 15:19:48 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:19:52 - agent - INFO - ==================================================
2025-06-12 15:19:52 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:19:52 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:19:52 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:19:52 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:19:52 - agent - INFO - 依赖包版本:
2025-06-12 15:19:52 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:19:52 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:19:52 - agent - INFO -   openai: 1.86.0
2025-06-12 15:19:52 - agent - INFO - ==================================================
2025-06-12 15:20:40 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:20:41 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:20:41 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tools', 'tool_names'}
2025-06-12 15:21:03 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:21:03 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:21:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:21:04 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tools', 'tool_names'}
2025-06-12 15:21:04 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:21:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:21:04 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tools', 'tool_names'}
2025-06-12 15:21:04 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:21:04 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:21:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:21:04 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tools', 'tool_names'}
2025-06-12 15:21:04 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:21:04 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:21:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:21:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:21:04 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tools', 'tool_names'}
2025-06-12 15:21:21 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:21:24 - agent - INFO - ==================================================
2025-06-12 15:21:24 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:21:24 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:21:24 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:21:24 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:21:24 - agent - INFO - 依赖包版本:
2025-06-12 15:21:24 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:21:24 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:21:24 - agent - INFO -   openai: 1.86.0
2025-06-12 15:21:24 - agent - INFO - ==================================================
2025-06-12 15:22:03 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:22:03 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:22:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:22:04 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:22:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:22:04 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:22:04 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:22:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:22:04 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:22:04 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:22:04 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:22:04 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:22:13 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:22:16 - agent - INFO - ==================================================
2025-06-12 15:22:16 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:22:16 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:22:16 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:22:16 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:22:16 - agent - INFO - 依赖包版本:
2025-06-12 15:22:16 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:22:16 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:22:16 - agent - INFO -   openai: 1.86.0
2025-06-12 15:22:16 - agent - INFO - ==================================================
2025-06-12 15:22:50 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:22:50 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:22:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:22:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:22:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:22:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:22:50 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:22:50 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:22:50 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:22:51 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:22:51 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:22:51 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:22:51 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:22:51 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:22:51 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:22:51 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:22:51 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:22:51 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:22:51 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:22:51 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:23:04 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:23:07 - agent - INFO - ==================================================
2025-06-12 15:23:07 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:23:07 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:23:07 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:23:07 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:23:07 - agent - INFO - 依赖包版本:
2025-06-12 15:23:07 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:23:07 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:23:07 - agent - INFO -   openai: 1.86.0
2025-06-12 15:23:07 - agent - INFO - ==================================================
2025-06-12 15:23:15 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:23:18 - agent - INFO - ==================================================
2025-06-12 15:23:18 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:23:18 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:23:18 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:23:18 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:23:18 - agent - INFO - 依赖包版本:
2025-06-12 15:23:18 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:23:18 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:23:18 - agent - INFO -   openai: 1.86.0
2025-06-12 15:23:18 - agent - INFO - ==================================================
2025-06-12 15:23:19 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:23:19 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:23:20 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:23:20 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:23:20 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:23:20 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:23:20 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:23:20 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:23:34 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:23:37 - agent - INFO - ==================================================
2025-06-12 15:23:37 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:23:37 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:23:37 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:23:37 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:23:37 - agent - INFO - 依赖包版本:
2025-06-12 15:23:37 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:23:37 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:23:37 - agent - INFO -   openai: 1.86.0
2025-06-12 15:23:37 - agent - INFO - ==================================================
2025-06-12 15:23:45 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:23:48 - agent - INFO - ==================================================
2025-06-12 15:23:48 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:23:48 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:23:48 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:23:48 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:23:48 - agent - INFO - 依赖包版本:
2025-06-12 15:23:48 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:23:48 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:23:48 - agent - INFO -   openai: 1.86.0
2025-06-12 15:23:48 - agent - INFO - ==================================================
2025-06-12 15:24:15 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:24:15 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:24:16 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:24:16 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:24:16 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:24:16 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:24:16 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:24:16 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:24:25 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:24:29 - agent - INFO - ==================================================
2025-06-12 15:24:29 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:24:29 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:24:29 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:24:29 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:24:29 - agent - INFO - 依赖包版本:
2025-06-12 15:24:29 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:24:29 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:24:29 - agent - INFO -   openai: 1.86.0
2025-06-12 15:24:29 - agent - INFO - ==================================================
2025-06-12 15:24:37 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:24:41 - agent - INFO - ==================================================
2025-06-12 15:24:41 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:24:41 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:24:41 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:24:41 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:24:41 - agent - INFO - 依赖包版本:
2025-06-12 15:24:41 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:24:41 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:24:41 - agent - INFO -   openai: 1.86.0
2025-06-12 15:24:41 - agent - INFO - ==================================================
2025-06-12 15:25:26 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:25:27 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:25:27 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:25:28 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:25:28 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:25:28 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:25:28 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:25:28 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:25:28 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:25:29 - agent - INFO - ==================================================
2025-06-12 15:25:29 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:25:29 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:25:29 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:25:29 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:25:29 - agent - INFO - 依赖包版本:
2025-06-12 15:25:29 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:25:29 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:25:29 - agent - INFO -   openai: 1.86.0
2025-06-12 15:25:29 - agent - INFO - ==================================================
2025-06-12 15:25:58 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:26:02 - agent - INFO - ==================================================
2025-06-12 15:26:02 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:26:02 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:26:02 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:26:02 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:26:02 - agent - INFO - 依赖包版本:
2025-06-12 15:26:02 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:26:02 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:26:02 - agent - INFO -   openai: 1.86.0
2025-06-12 15:26:02 - agent - INFO - ==================================================
2025-06-12 15:26:15 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:26:18 - agent - INFO - ==================================================
2025-06-12 15:26:18 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:26:18 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:26:18 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:26:18 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:26:18 - agent - INFO - 依赖包版本:
2025-06-12 15:26:18 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:26:18 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:26:18 - agent - INFO -   openai: 1.86.0
2025-06-12 15:26:18 - agent - INFO - ==================================================
2025-06-12 15:26:30 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:26:30 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:26:31 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:26:31 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:26:31 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:26:31 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:26:31 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:26:31 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:26:31 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: AI助手, 工具数量: 4
2025-06-12 15:26:31 - agent.assistant_agent - INFO - ✅ 初始化LangChain助手代理: AI助手, 工具数量: 4
2025-06-12 15:26:31 - agent.assistant_agent - INFO - 🔧 可用工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:26:31 - agent.assistant_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting)
2025-06-12 15:26:31 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:26:31 - agent.langchain_agent - INFO - 📝 输入内容: 你好
2025-06-12 15:26:33 - agent.langchain_agent - ERROR - ❌ Agent执行失败: One input key expected got ['input', 'tools', 'agent_scratchpad', 'tool_names']
2025-06-12 15:26:33 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 1.863s
2025-06-12 15:26:45 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:26:48 - agent - INFO - ==================================================
2025-06-12 15:26:48 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:26:48 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:26:48 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:26:48 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:26:48 - agent - INFO - 依赖包版本:
2025-06-12 15:26:48 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:26:48 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:26:48 - agent - INFO -   openai: 1.86.0
2025-06-12 15:26:48 - agent - INFO - ==================================================
2025-06-12 15:26:51 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:26:52 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:26:52 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:26:52 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: 测试用例编写助手, 工具数量: 0
2025-06-12 15:26:52 - agent.testcase_agent - INFO - ✅ 初始化LangChain测试用例编写代理: 测试用例编写助手
2025-06-12 15:26:52 - agent.testcase_agent - INFO - 🔧 可用工具: []
2025-06-12 15:26:52 - agent.testcase_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting) for Test Case Writing
2025-06-12 15:26:52 - agent.api - INFO - 创建测试用例编写代理: b2e314be-59d4-4884-974b-e152aeea6f04
2025-06-12 15:26:54 - agent.api - INFO - 🚀 开始处理查询请求 - 查询内容: 用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 支持记住密码功能
3. 连续3次登录失败后锁定账户30分钟
4. 登录成功后跳转到主页面
5. 支持忘记密码功能，通过邮箱重置密码

...
2025-06-12 15:26:54 - agent.api - INFO - 📋 请求参数 - agent_id: b2e314be-59d4-4884-974b-e152aeea6f04, stream: True
2025-06-12 15:26:54 - agent.api - INFO - ♻️ 使用现有代理: b2e314be-59d4-4884-974b-e152aeea6f04
2025-06-12 15:26:54 - agent.api - INFO - 📦 获取代理实例完成 - 类型: TestCaseAgent (耗时: 0.000s)
2025-06-12 15:26:54 - agent.api - INFO - 🌊 开始流式响应处理...
2025-06-12 15:26:54 - agent.api - INFO - 🌊 开始为代理 b2e314be-59d4-4884-974b-e152aeea6f04 生成流式响应
2025-06-12 15:26:54 - agent.api - INFO - 🔍 代理类型: TestCaseAgent
2025-06-12 15:26:54 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: TestCaseAgent, 流式: True
2025-06-12 15:26:54 - agent.langchain_agent - INFO - 📝 输入内容: 用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 支持记住密码功能
3. 连续3次登录失败后锁定账户30分钟
4. 登录成功后跳转到主页面
5. 支持忘记密码功能，通过邮箱重置密码

技术要求：
- 密码需要加密存储
- 登录状态保持24小时
- 支持手机端和PC端
- 响应时间不超过2秒
2025-06-12 15:26:54 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 0.000s
2025-06-12 15:26:54 - agent.api - INFO - ⚡ 流式生成器初始化完成 (耗时: 0.000s)
2025-06-12 15:27:23 - agent.api - INFO - 🎯 收到第一个响应块 (距离开始: 28.878s)
2025-06-12 15:27:23 - agent.api - INFO - 📦 处理块 #0 - 阶段: unknown, 状态: unknown, 长度: 78, 间隔: 28.878s
2025-06-12 15:27:23 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:27:26 - agent - INFO - ==================================================
2025-06-12 15:27:26 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:27:26 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:27:26 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:27:26 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:27:26 - agent - INFO - 依赖包版本:
2025-06-12 15:27:26 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:27:26 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:27:26 - agent - INFO -   openai: 1.86.0
2025-06-12 15:27:26 - agent - INFO - ==================================================
2025-06-12 15:27:51 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:27:51 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:27:52 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:27:52 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:27:52 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:27:52 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:27:52 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:27:52 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:28:04 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:28:07 - agent - INFO - ==================================================
2025-06-12 15:28:07 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:28:07 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:28:07 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:28:07 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:28:07 - agent - INFO - 依赖包版本:
2025-06-12 15:28:07 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:28:07 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:28:07 - agent - INFO -   openai: 1.86.0
2025-06-12 15:28:07 - agent - INFO - ==================================================
2025-06-12 15:28:17 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:28:20 - agent - INFO - ==================================================
2025-06-12 15:28:20 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:28:20 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:28:20 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:28:20 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:28:20 - agent - INFO - 依赖包版本:
2025-06-12 15:28:20 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:28:20 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:28:20 - agent - INFO -   openai: 1.86.0
2025-06-12 15:28:20 - agent - INFO - ==================================================
2025-06-12 15:28:31 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:28:34 - agent - INFO - ==================================================
2025-06-12 15:28:34 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:28:34 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:28:34 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:28:34 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:28:34 - agent - INFO - 依赖包版本:
2025-06-12 15:28:34 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:28:34 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:28:34 - agent - INFO -   openai: 1.86.0
2025-06-12 15:28:34 - agent - INFO - ==================================================
2025-06-12 15:28:36 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:28:39 - agent - INFO - ==================================================
2025-06-12 15:28:39 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:28:39 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:28:39 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:28:39 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:28:39 - agent - INFO - 依赖包版本:
2025-06-12 15:28:39 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:28:39 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:28:39 - agent - INFO -   openai: 1.86.0
2025-06-12 15:28:39 - agent - INFO - ==================================================
2025-06-12 15:28:49 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:28:49 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:28:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:28:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:28:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:28:50 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:28:50 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:28:50 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:28:50 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: AI助手, 工具数量: 4
2025-06-12 15:28:50 - agent.assistant_agent - INFO - ✅ 初始化LangChain助手代理: AI助手, 工具数量: 4
2025-06-12 15:28:50 - agent.assistant_agent - INFO - 🔧 可用工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:28:50 - agent.assistant_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting)
2025-06-12 15:28:50 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:28:50 - agent.langchain_agent - INFO - 📝 输入内容: 你好
2025-06-12 15:28:52 - agent.langchain_agent - ERROR - ❌ Agent执行失败: One input key expected got ['tool_names', 'tools', 'input']
2025-06-12 15:28:52 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 2.604s
2025-06-12 15:29:04 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:29:07 - agent - INFO - ==================================================
2025-06-12 15:29:07 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:29:07 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:29:07 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:29:07 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:29:07 - agent - INFO - 依赖包版本:
2025-06-12 15:29:07 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:29:07 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:29:07 - agent - INFO -   openai: 1.86.0
2025-06-12 15:29:07 - agent - INFO - ==================================================
2025-06-12 15:29:19 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:29:22 - agent - INFO - ==================================================
2025-06-12 15:29:22 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:29:22 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:29:22 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:29:22 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:29:22 - agent - INFO - 依赖包版本:
2025-06-12 15:29:22 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:29:22 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:29:22 - agent - INFO -   openai: 1.86.0
2025-06-12 15:29:22 - agent - INFO - ==================================================
2025-06-12 15:29:27 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:29:30 - agent - INFO - ==================================================
2025-06-12 15:29:30 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:29:30 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:29:30 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:29:30 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:29:30 - agent - INFO - 依赖包版本:
2025-06-12 15:29:30 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:29:30 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:29:30 - agent - INFO -   openai: 1.86.0
2025-06-12 15:29:30 - agent - INFO - ==================================================
2025-06-12 15:29:38 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:29:41 - agent - INFO - ==================================================
2025-06-12 15:29:41 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:29:41 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:29:41 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:29:41 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:29:41 - agent - INFO - 依赖包版本:
2025-06-12 15:29:41 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:29:41 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:29:41 - agent - INFO -   openai: 1.86.0
2025-06-12 15:29:41 - agent - INFO - ==================================================
2025-06-12 15:29:42 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:29:45 - agent - INFO - ==================================================
2025-06-12 15:29:45 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:29:45 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:29:45 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:29:45 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:29:45 - agent - INFO - 依赖包版本:
2025-06-12 15:29:45 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:29:45 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:29:45 - agent - INFO -   openai: 1.86.0
2025-06-12 15:29:45 - agent - INFO - ==================================================
2025-06-12 15:29:53 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:29:53 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:29:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:29:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:29:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:29:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:29:54 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:29:54 - agent.langchain_agent - ERROR - ❌ 创建Agent Executor失败: Prompt missing required variables: {'tool_names', 'tools'}
2025-06-12 15:30:09 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:30:15 - agent - INFO - ==================================================
2025-06-12 15:30:15 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:30:15 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:30:15 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:30:15 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:30:15 - agent - INFO - 依赖包版本:
2025-06-12 15:30:15 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:30:15 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:30:15 - agent - INFO -   openai: 1.86.0
2025-06-12 15:30:15 - agent - INFO - ==================================================
2025-06-12 15:30:23 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:30:27 - agent - INFO - ==================================================
2025-06-12 15:30:27 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:30:27 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:30:27 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:30:27 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:30:27 - agent - INFO - 依赖包版本:
2025-06-12 15:30:27 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:30:27 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:30:27 - agent - INFO -   openai: 1.86.0
2025-06-12 15:30:27 - agent - INFO - ==================================================
2025-06-12 15:30:40 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:30:43 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:30:43 - agent - INFO - ==================================================
2025-06-12 15:30:43 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:30:43 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:30:43 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:30:43 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:30:43 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:30:43 - agent - INFO - 依赖包版本:
2025-06-12 15:30:43 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:30:43 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:30:43 - agent - INFO -   openai: 1.86.0
2025-06-12 15:30:43 - agent - INFO - ==================================================
2025-06-12 15:30:44 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:30:44 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:30:44 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:30:44 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:30:44 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:30:44 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:30:44 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: AI助手, 工具数量: 4
2025-06-12 15:30:44 - agent.assistant_agent - INFO - ✅ 初始化LangChain助手代理: AI助手, 工具数量: 4
2025-06-12 15:30:44 - agent.assistant_agent - INFO - 🔧 可用工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:30:44 - agent.assistant_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting)
2025-06-12 15:30:44 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:30:44 - agent.langchain_agent - INFO - 📝 输入内容: 你好
2025-06-12 15:30:46 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 2.014s
2025-06-12 15:31:00 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:31:00 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:31:01 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:31:01 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:31:01 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:31:01 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:31:01 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:31:01 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:31:01 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: AI助手, 工具数量: 4
2025-06-12 15:31:01 - agent.assistant_agent - INFO - ✅ 初始化LangChain助手代理: AI助手, 工具数量: 4
2025-06-12 15:31:01 - agent.assistant_agent - INFO - 🔧 可用工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:31:01 - agent.assistant_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting)
2025-06-12 15:31:01 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:31:01 - agent.langchain_agent - INFO - 📝 输入内容: 你好，请介绍一下你自己
2025-06-12 15:31:08 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 6.763s
2025-06-12 15:31:08 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:31:08 - agent.langchain_agent - INFO - 📝 输入内容: 请计算 15 + 27 的结果
2025-06-12 15:31:11 - agent.langchain_tools - INFO - 🔧 执行LangChain适配工具: calculator
2025-06-12 15:31:11 - agent.tools - INFO - 执行计算: 15 + 27
2025-06-12 15:31:11 - agent.langchain_tools - INFO - ✅ 工具执行成功: calculator
2025-06-12 15:31:18 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 9.809s
2025-06-12 15:31:18 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:31:18 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:31:18 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:31:18 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: 测试用例编写助手, 工具数量: 0
2025-06-12 15:31:18 - agent.testcase_agent - INFO - ✅ 初始化LangChain测试用例编写代理: 测试用例编写助手
2025-06-12 15:31:18 - agent.testcase_agent - INFO - 🔧 可用工具: []
2025-06-12 15:31:18 - agent.testcase_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting) for Test Case Writing
2025-06-12 15:31:18 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: TestCaseAgent, 流式: False
2025-06-12 15:31:18 - agent.langchain_agent - INFO - 📝 输入内容: 请为以下需求编写测试用例：
        用户登录功能需求：
        1. 用户可以通过用户名和密码登录
        2. 登录失败3次后账户锁定30分钟
        3. 支持记住登录状态功能
        
2025-06-12 15:31:42 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 24.185s
2025-06-12 15:31:42 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:31:42 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:31:42 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:31:42 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:31:42 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:31:42 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:31:42 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:31:42 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:31:42 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: AI助手, 工具数量: 4
2025-06-12 15:31:42 - agent.assistant_agent - INFO - ✅ 初始化LangChain助手代理: AI助手, 工具数量: 4
2025-06-12 15:31:42 - agent.assistant_agent - INFO - 🔧 可用工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:31:42 - agent.assistant_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting)
2025-06-12 15:31:42 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: True
2025-06-12 15:31:42 - agent.langchain_agent - INFO - 📝 输入内容: 请详细解释什么是人工智能
2025-06-12 15:31:42 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 0.000s
2025-06-12 15:31:42 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: 测试用例编写助手
2025-06-12 15:31:43 - agent.langchain_tools - INFO - 🔧 工具转换完成: 0/0 个工具转换成功
2025-06-12 15:31:43 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:31:43 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: 测试用例编写助手, 工具数量: 0
2025-06-12 15:31:43 - agent.testcase_agent - INFO - ✅ 初始化LangChain测试用例编写代理: 测试用例编写助手
2025-06-12 15:31:43 - agent.testcase_agent - INFO - 🔧 可用工具: []
2025-06-12 15:31:43 - agent.testcase_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting) for Test Case Writing
2025-06-12 15:31:43 - agent.api - INFO - 创建测试用例编写代理: 2bb7b6bc-e5fd-4061-9bd6-68e3b322bca2
2025-06-12 15:31:45 - agent.api - INFO - 🚀 开始处理查询请求 - 查询内容: 用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 支持记住密码功能
3. 连续3次登录失败后锁定账户30分钟
4. 登录成功后跳转到主页面
5. 支持忘记密码功能，通过邮箱重置密码

...
2025-06-12 15:31:45 - agent.api - INFO - 📋 请求参数 - agent_id: 2bb7b6bc-e5fd-4061-9bd6-68e3b322bca2, stream: True
2025-06-12 15:31:45 - agent.api - INFO - ♻️ 使用现有代理: 2bb7b6bc-e5fd-4061-9bd6-68e3b322bca2
2025-06-12 15:31:45 - agent.api - INFO - 📦 获取代理实例完成 - 类型: TestCaseAgent (耗时: 0.000s)
2025-06-12 15:31:45 - agent.api - INFO - 🌊 开始流式响应处理...
2025-06-12 15:31:45 - agent.api - INFO - 🌊 开始为代理 2bb7b6bc-e5fd-4061-9bd6-68e3b322bca2 生成流式响应
2025-06-12 15:31:45 - agent.api - INFO - 🔍 代理类型: TestCaseAgent
2025-06-12 15:31:45 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: TestCaseAgent, 流式: True
2025-06-12 15:31:45 - agent.langchain_agent - INFO - 📝 输入内容: 用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 支持记住密码功能
3. 连续3次登录失败后锁定账户30分钟
4. 登录成功后跳转到主页面
5. 支持忘记密码功能，通过邮箱重置密码

技术要求：
- 密码需要加密存储
- 登录状态保持24小时
- 支持手机端和PC端
- 响应时间不超过2秒
2025-06-12 15:31:45 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 0.000s
2025-06-12 15:31:45 - agent.api - INFO - ⚡ 流式生成器初始化完成 (耗时: 0.000s)
2025-06-12 15:31:46 - agent.langchain_tools - INFO - 🔧 执行LangChain适配工具: search
2025-06-12 15:31:46 - agent.tools - INFO - 执行搜索: 什么是人工智能  

2025-06-12 15:31:46 - agent.langchain_tools - INFO - ✅ 工具执行成功: search
2025-06-12 15:31:54 - agent.assistant_agent - INFO - 初始化默认工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:31:54 - agent.langchain_agent - INFO - 🤖 初始化LangChain Agent: AI助手
2025-06-12 15:31:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: search
2025-06-12 15:31:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: weather
2025-06-12 15:31:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: calculator
2025-06-12 15:31:54 - agent.langchain_tools - INFO - ✅ 成功转换工具: time
2025-06-12 15:31:54 - agent.langchain_tools - INFO - 🔧 工具转换完成: 4/4 个工具转换成功
2025-06-12 15:31:54 - agent.langchain_agent - INFO - ✅ Agent Executor创建成功
2025-06-12 15:31:54 - agent.langchain_agent - INFO - ✅ LangChain Agent初始化完成: AI助手, 工具数量: 4
2025-06-12 15:31:54 - agent.assistant_agent - INFO - ✅ 初始化LangChain助手代理: AI助手, 工具数量: 4
2025-06-12 15:31:54 - agent.assistant_agent - INFO - 🔧 可用工具: ['search', 'weather', 'calculator', 'time']
2025-06-12 15:31:54 - agent.assistant_agent - INFO - 🤖 Agent类型: ReAct (Reasoning + Acting)
2025-06-12 15:31:54 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:31:54 - agent.langchain_agent - INFO - 📝 输入内容: 我的名字是张三
2025-06-12 15:31:58 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 3.915s
2025-06-12 15:31:58 - agent.langchain_agent - INFO - 🧠 LangChain Agent开始处理 - 类型: AssistantAgent, 流式: False
2025-06-12 15:31:58 - agent.langchain_agent - INFO - 📝 输入内容: 你还记得我的名字吗？
2025-06-12 15:32:01 - agent.langchain_agent - INFO - 🎉 Agent处理完成 - 总耗时: 3.271s
2025-06-12 15:32:07 - agent.api - INFO - 🎯 收到第一个响应块 (距离开始: 22.390s)
2025-06-12 15:32:07 - agent.api - INFO - 📦 处理块 #0 - 阶段: unknown, 状态: unknown, 长度: 0, 间隔: 22.390s
2025-06-12 15:32:07 - agent.api - INFO - ✅ 代理 2bb7b6bc-e5fd-4061-9bd6-68e3b322bca2 完成流式响应
2025-06-12 15:32:07 - agent.api - INFO - 📊 流式响应统计 - 总块数: 1, 总耗时: 22.395s
2025-06-12 15:32:07 - agent.api - INFO - ⏱️ 首块延迟: 22.390s
2025-06-12 15:35:39 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:35:42 - agent - INFO - ==================================================
2025-06-12 15:35:42 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:35:42 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:35:42 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:35:42 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:35:42 - agent - INFO - 依赖包版本:
2025-06-12 15:35:42 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:35:42 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:35:42 - agent - INFO -   openai: 1.86.0
2025-06-12 15:35:42 - agent - INFO - ==================================================
2025-06-12 15:35:51 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:35:54 - agent - INFO - ==================================================
2025-06-12 15:35:54 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:35:54 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:35:54 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:35:54 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:35:54 - agent - INFO - 依赖包版本:
2025-06-12 15:35:54 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:35:54 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:35:54 - agent - INFO -   openai: 1.86.0
2025-06-12 15:35:54 - agent - INFO - ==================================================
2025-06-12 15:35:59 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:36:02 - agent - INFO - ==================================================
2025-06-12 15:36:02 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:36:02 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:36:02 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:36:02 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:36:02 - agent - INFO - 依赖包版本:
2025-06-12 15:36:02 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:36:02 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:36:02 - agent - INFO -   openai: 1.86.0
2025-06-12 15:36:02 - agent - INFO - ==================================================
2025-06-12 15:36:08 - agent - INFO - 正在关闭 AI Agent 框架服务
2025-06-12 15:36:11 - agent - INFO - ==================================================
2025-06-12 15:36:11 - agent - INFO - 启动 AI Agent 框架服务
2025-06-12 15:36:11 - agent - INFO - Python 版本: 3.12.10 (v3.12.10:0cc81280367, Apr  8 2025, 08:46:59) [Clang 13.0.0 (clang-1300.0.29.30)]
2025-06-12 15:36:11 - agent - INFO - 使用大模型: qwen-plus
2025-06-12 15:36:11 - agent - INFO - 服务地址: http://0.0.0.0:8000
2025-06-12 15:36:11 - agent - INFO - 依赖包版本:
2025-06-12 15:36:11 - agent - INFO -   langchain-openai: 0.0.5
2025-06-12 15:36:11 - agent - INFO -   tiktoken: 0.5.2
2025-06-12 15:36:11 - agent - INFO -   openai: 1.86.0
2025-06-12 15:36:11 - agent - INFO - ==================================================
2025-06-12 15:36:16 - agent - INFO - 正在关闭 AI Agent 框架服务
